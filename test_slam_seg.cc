/**
 * @Autor: AI Assistant
 * @time: 2025-08-19
 * @description: 测试SLAM分割任务的简单程序
 */

#include <iostream>
#include <opencv2/opencv.hpp>
#include "include/task/slam_seg.h"
#include "include/data_type.h"
#include "include/shunzao_ai_lib.h"

int main() {
    std::cout << "Testing SlamSeg implementation..." << std::endl;
    
    // 测试模型路径
    const char* model_path = "./model/pidnet_pcq_x527.nb";
    
    try {
        // 创建SlamSeg实例
        SlamSeg slam_seg(model_path);
        std::cout << "SlamSeg instance created successfully" << std::endl;
        
        // 测试配置加载
        std::string config = R"({
            "confidence_threshold": 0.5,
            "debug_show_result": 1
        })";
        
        int config_result = slam_seg.loadconfig(config);
        if (config_result == 0) {
            std::cout << "Config loaded successfully" << std::endl;
        } else {
            std::cout << "Config loading failed" << std::endl;
        }
        
        // 创建测试图像
        cv::Mat test_img = cv::Mat::zeros(1024, 2048, CV_8UC3);
        std::cout << "Test image created: " << test_img.cols << "x" << test_img.rows << std::endl;
        
        // 创建输入参数
        InputParam inp;
        inp.coreid = 0;
        
        // 测试推理（注意：这里可能会失败，因为模型文件可能不存在）
        std::cout << "Testing inference..." << std::endl;
        bool inference_result = slam_seg.run_inference_with_time(test_img, &inp);
        
        if (inference_result) {
            std::cout << "Inference completed successfully" << std::endl;
            
            // 获取预测结果
            int size = 0;
            std::vector<SegMaskFlag> results = slam_seg.get_prediction(&size);
            std::cout << "Got " << results.size() << " segmentation results" << std::endl;
            
            for (size_t i = 0; i < results.size(); i++) {
                const SegMask& mask = results[i].mask;
                std::cout << "Result " << i << ":" << std::endl;
                std::cout << "  - Mask size: " << mask.width << "x" << mask.height << std::endl;
                std::cout << "  - Number of classes: " << mask.num_classes << std::endl;
                std::cout << "  - Confidence: " << results[i].confidence << std::endl;
                std::cout << "  - Flag: " << results[i].flag << std::endl;
            }
        } else {
            std::cout << "Inference failed (expected if model file doesn't exist)" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return -1;
    }
    
    std::cout << "SlamSeg test completed" << std::endl;
    return 0;
}
