
# 添加库路径到动态链接路径
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:./lib
# export LD_LIBRARY_PATH=/mnt/UDISK/lib:$LD_LIBRARY_PATH
# 设置变量
# MODEL_FILE="./model/yolov5s-sim-640.nb"          # 模型路径
MODEL_FILE="./model/pidnet_uint8_x527.nb"          # 模型路径
# MODEL_FILE="./model/yolov8s_uint8_x527.nb"          # 模型路径
# MODEL_FILE ="./model/line_uint8_x536.nb"
CONFIG_FILE="./models.json"      # 配置文件路径
IMAGES_DIR="./input_data"            # 输入图片文件夹
OUTPUT_DIR="./output"            # 结果输出目录
RESULT_DIR="./result"
MODEL_ID=4                      #task.h
LOG_LEVEL=0
INPUT_TYPE="png"
INPUT_HEIGHT=1024
INPUT_WIDTH=2048

# 可执行程序路径（请根据实际情况修改）
EXECUTABLE="./shunzao_ai_demo"

# 创建输出目录（如果不存在）
mkdir -p $OUTPUT_DIR
mkdir -p $RESULT_DIR

# 执行程序
$EXECUTABLE \
    $MODEL_FILE \
    dummy_input \
    --model_file=$MODEL_FILE \
    --config_file=$CONFIG_FILE \
    --input_type=$INPUT_TYPE \
    --model_id=$MODEL_ID \
    --images_dir=$IMAGES_DIR \
    --output_dir=$OUTPUT_DIR \
    --input_height=$INPUT_HEIGHT \
    --input_width=$INPUT_WIDTH \
    --log_level=$LOG_LEVEL
