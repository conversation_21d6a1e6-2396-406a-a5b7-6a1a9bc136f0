#include "line_det.h"
#include "data_type.h"
#include "line_decode.h"
#include "utils.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include "shunzao_ai_task.h"


LineDet::LineDet(const char* model_path)
{
    if (!setup(model_path)) {
        std::cerr << "[GroundDet] Constructor failed to setup with model: " << model_path << std::endl;
    }
    res_addr_ = (char*)malloc(max_data_size_);
}

LineDet::~LineDet()
{
    if(res_addr_){
        delete res_addr_;
        res_addr_ = nullptr;
    }
}

bool LineDet::setup(const std::string& model_path)
{
    nn_in_width_ = input_h;
    nn_in_height_ = input_w;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}

bool LineDet::run_inference_with_time(cv::Mat& img, InputParam* inp) {
    int ret = 0;
    auto total_start = std::chrono::high_resolution_clock::now();

    // 1. 预处理阶段
    std::cout << "[Line] Preprocess start" << std::endl;  // 添加开始标志
    auto preprocess_start = std::chrono::high_resolution_clock::now();
    file_data = this->preprocess(img, inp->coreid, &file_size);
    auto preprocess_end = std::chrono::high_resolution_clock::now();
    auto preprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(preprocess_end - preprocess_start);
    std::cout << "[Line] Preprocess: " << preprocess_time.count()/1000.0 << " ms" << std::endl;

    // 2. 输入加载阶段
    std::cout << "[Line] Load input start" << std::endl;  // 添加开始标志
    auto load_start = std::chrono::high_resolution_clock::now();
    ret = load_input_set_output(file_data, file_size);
    auto load_end = std::chrono::high_resolution_clock::now();
    auto load_time = std::chrono::duration_cast<std::chrono::microseconds>(load_end - load_start);
    std::cout << "[Line] Load input: " << load_time.count()/1000.0 << " ms" << std::endl;

    // 3. 实际推理阶段
    std::cout << "[Line] NPU inference start" << std::endl;  // 添加开始标志
    auto inference_start = std::chrono::high_resolution_clock::now();
    if (!run_once()) {
        std::cerr << "[LineDet] Network run failed." << std::endl;
        return false;
    }
    auto inference_end = std::chrono::high_resolution_clock::now();
    auto inference_time = std::chrono::duration_cast<std::chrono::microseconds>(inference_end - inference_start);
    std::cout << "[Line] NPU inference: " << inference_time.count()/1000.0 << " ms" << std::endl;

    // 4. 输出获取阶段
    std::cout << "[Line] Get output start" << std::endl;  // 添加开始标志
    auto output_start = std::chrono::high_resolution_clock::now();
    get_output_data(output_data);
    auto output_end = std::chrono::high_resolution_clock::now();
    auto output_time = std::chrono::duration_cast<std::chrono::microseconds>(output_end - output_start);
    std::cout << "[Line] Get output: " << output_time.count()/1000.0 << " ms" << std::endl;

    // 5. 后处理阶段
    std::cout << "[Line] Postprocess start" << std::endl;  // 添加开始标志
    auto postprocess_start = std::chrono::high_resolution_clock::now();
    if (this->postprocess(img, output_data) != 0) {
        std::cerr << "[LineDet] postprocess failed." << std::endl;
        return false;
    }
    auto postprocess_end = std::chrono::high_resolution_clock::now();
    auto postprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(postprocess_end - postprocess_start);
    std::cout << "[Line] Postprocess: " << postprocess_time.count()/1000.0 << " ms" << std::endl;

    // 6. 内存释放阶段
    // 注意：清理阶段被注释，不添加开始标志

    // 总时间统计
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(total_end - total_start);
    
    std::cout << "\n[Summary] Total pipeline time: " << total_time.count() << " μs\n";
    std::cout << "  Preprocess: " << (100.0 * preprocess_time.count() / total_time.count()) << "%\n";
    std::cout << "  Load input: " << (100.0 * load_time.count() / total_time.count()) << "%\n";
    std::cout << "  Inference:  " << (100.0 * inference_time.count() / total_time.count()) << "%\n";
    std::cout << "  Get output: " << (100.0 * output_time.count() / total_time.count()) << "%\n";
    std::cout << "  Postprocess:" << (100.0 * postprocess_time.count() / total_time.count()) << "%\n";

    return true;
}

bool LineDet::run_inference(cv::Mat& img, InputParam* inp) {

    std::cout << "[LineDet] Running inference..." << std::endl;
    int ret = 0;

    file_data = this->preprocess(img, inp->coreid, &file_size);
    std::cout << "[LineDet] Preprocess done." << std::endl;

    ret = load_input_set_output(file_data, file_size);
    // auto start = std::chrono::high_resolution_clock::now();
    if (!run_once()) {
        std::cerr << "[LineDet] Network run failed." << std::endl;
        return false;
    }
    // auto end = std::chrono::high_resolution_clock::now();
    // auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    // std::cout << "NPU inference time: " << duration.count() << " μs\n";
    get_output_data(output_data);  // 获取网络最终的输出矩阵,存储到gound_output_data中
    if (!output_data) {
        std::cerr << "[LineDet] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(img, output_data) !=0){
        std::cerr << "[LineDet] postprocess failed." << std::endl;
        return -1;
    }

    // 释放存储模型output_data的内存
    delete_output_data(output_data);

    return true;
}

bool LineDet::run_inference(cv::Mat& img, InputParam* inp,shunzaoAiTask* outer) {

    std::cout << "[LineDet] Running inference..." << std::endl;
    int ret = 0;
    struct  timeval run_start{},run_end{};
    gettimeofday(&run_start,nullptr);

    file_data = this->preprocess(img, inp->coreid, &file_size);
    // std::cout << "[LineDet] Preprocess done." << std::endl;

    ret = load_input_set_output(file_data, file_size);
    gettimeofday(&run_end,nullptr);
    long long preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
    (run_end.tv_usec - run_start.tv_usec);
    outer->setPreprocessTime(preprocess_time);

    gettimeofday(&run_start,nullptr);
    if (!run_once()) {
        std::cerr << "[LineDet] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵,存储到gound_output_data中
    gettimeofday(&run_end,nullptr);
    preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 + (run_end.tv_usec - run_start.tv_usec);  // 赋值
    outer->setInferenceTime(preprocess_time);

    
    if (!output_data) {
        std::cerr << "[LineDet] Failed to get output." << std::endl;
        return false;
    }
    gettimeofday(&run_start,nullptr);
    if (this->postprocess(img, output_data) !=0){
        std::cerr << "[LineDet] postprocess failed." << std::endl;
        return -1;
    }
    gettimeofday(&run_end,nullptr);
    preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +(run_end.tv_usec - run_start.tv_usec);
    outer->setPostprocessTime(preprocess_time);
    outer->setCount();
    
    // 释放存储模型output_data的内存
    delete_output_data(output_data);

    return true;
}

extern float ymax_th;
std::vector<BBoxFlag> LineDet::get_prediction(int* size) {

	std::vector<BBoxFlag> tmp;
	
	float* result_f = (float*)res_addr_;

	for (int i = 0; i < results_.size(); i++) {
        float flag = 0;
		if(results_[i].ymax > ymax_th){
			flag = 1;
		}
		// std::cout<< "resultssssss66666666s "<<bbox_results_[i].label<<std::endl;
        tmp.push_back(BBoxFlag{results_[i], flag});
	}


	// result_f[0] = float(tmp.size());

	// memcpy(res_addr_ , tmp.data(), int(tmp.size())*sizeof(BBoxFlag));
    
    // *size = int(tmp.size())*sizeof(BBoxFlag) + 4 + 12 + 1;
    // // ai_msg_encode(res_addr_, *size, image_mean_);
    return tmp;
}

int LineDet::loadconfig(std::string config_string){
	std::cout<<"chrisyoung:loading config string "<<config_string<<std::endl;
	rapidjson::Document document;
    document.Parse(config_string.data());
    if (document.HasParseError()) {
        std::cout<<"chrisyoung:loading GroundCls config file error"<<std::endl;
        return -1;
    }
	if(document.HasMember("config")){
        rapidjson::Value& model_value = document["config"];
        if(model_value.HasMember("score_threshold")){
            score_threshold_ = model_value["score_threshold"].GetFloat();
        }
        if(model_value.HasMember("nms_threshold")){
            nms_threshold_ = model_value["nms_threshold"].GetFloat();
        }
		if(model_value.HasMember("mean_score")){
			mean_score_.clear();
            auto mean_score_array = model_value["mean_score"].GetArray();
			for (size_t i = 0; i < mean_score_array.Size(); i++)
			{
				mean_score_.push_back(mean_score_array[i].GetFloat());
			}
        }
		// if(model_value.HasMember("heads_classes")){
		// 	heads_classes_.clear();
        //     auto heads_classes_array = model_value["heads_classes"].GetArray();
		// 	for (size_t i = 0; i < heads_classes_array.Size(); i++)
		// 	{
		// 		heads_classes_.push_back(heads_classes_array[i].GetInt());
		// 	}
        // }
		// if(model_value.HasMember("postprocess_method_type")){
        //     postprocess_method_type_ = model_value["postprocess_method_type"].GetInt();
        // }	
    }

	printf("yaoshi:current params %f %f %d",score_threshold_, nms_threshold_);
    for (size_t i = 0; i < int(mean_score_.size()); i++)
    {
        printf(" %f ", mean_score_[i]);
    }
	// for (size_t i = 0; i < int(heads_classes_.size()); i++)
    // {
    //     printf(" %f ", heads_classes_[i]);
    // }
    printf("\n");
	return 0;
}


int LineDet::postprocess(cv::Mat& img, float** output_tensor){

    std::cout<<"line postprocess..."<<std::endl;
    std::vector<BBox> det_boxes;
    results_.clear();
    float** output_head = new float*[3];    // 分配新的指针数组，用于存储每个检测任务头的3个尺寸tensor的指针

    for (int j = 0; j < 3; ++j) {
        output_head[j] = output_tensor[j];
    }
    det_boxes = line_post_process(output_head, class_num, input_w, input_h, score_threshold_);

    if (debug_show_result){
        int img_width = img.cols;
        int img_height = img.rows;
        float scale = std::min(input_w * 1.0 / img_width, input_h * 1.0 / img_height);
        float pad_w = (input_w - img_width * scale) / 2;
        float pad_h = (input_h - img_height * scale) / 2;

        int num_box=det_boxes.size();
        // num_box = NMS(det_boxes, nms_threshold_);
    
        for (int n=0;n<num_box;n++){
            if(det_boxes[n].label != -1){
                det_boxes[n].xmin = (det_boxes[n].xmin - pad_w) / scale;
                det_boxes[n].ymin = (det_boxes[n].ymin - pad_h) / scale;
                det_boxes[n].xmax = (det_boxes[n].xmax - pad_w) / scale;
                det_boxes[n].ymax = (det_boxes[n].ymax - pad_h) / scale;
    
                det_boxes[n].xmin = clamp(det_boxes[n].xmin, 0.0f, (float)img_width);
                det_boxes[n].ymin = clamp(det_boxes[n].ymin, 0.0f, (float)img_height);
                det_boxes[n].xmax = clamp(det_boxes[n].xmax, 0.0f, (float)img_width);
                det_boxes[n].ymax = clamp(det_boxes[n].ymax, 0.0f, (float)img_height);
                results_.push_back(det_boxes[n]);
                // printf("xmin = %f,ymin = %f, xmax = %f, ymax = %f, classId = %d, score = %f\n",det_boxes[n].xmin,det_boxes[n].ymin,det_boxes[n].xmax,det_boxes[n].ymax,det_boxes[n].label,det_boxes[n].score);
            }
        }            

    }

    // if (eval){
    //     continue;
    //     // 实现保存模型推理的原始结果，用于计算 mAP
    // }
        
    
    // draw_line(input_data, results_);
    // results_.clear();
    delete[] output_head;

    return 0;
}