#include "yolov8.h"
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <iomanip>
#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <unordered_map>
#include "utils.h"
#include "data_type.h"


std::vector<BBox> yolov8_post_process(float **outputs, int class_num, int input_img_w, int input_img_h, float score_threshold_)
{
    int ret = 0;
    std::vector<float> meshgrid;
    int headNum = 3;
    int strides[3] = {8, 16, 32};
    int mapSize[3][2] = {{80, 80}, {40, 40}, {20, 20}};

    if (meshgrid.empty())
    {
        ret = GenerateMeshgrid(headNum, mapSize, meshgrid);
    }

    float cls_temp = 0;
    float cls_vlaue = 0;
    int cls_index = 0;

    // float scale = std::min(input_w * 1.0 / img_width, input_h * 1.0 / img_height);
    // float pad_w = (input_w - img_width * scale) / 2;
    // float pad_h = (input_h - img_height * scale) / 2;

    std::vector<BBox> detectRects;

    int gridIndex = -2;
    float sfsum = 0;
    float locval = 0;
    float locvaltemp = 0;

    for (int index = 0; index < headNum; index++)
    {   
        if (class_num==1)
        {
            if (index != 0){
                continue;
            }
        }

        float *output = outputs[index];

        for (int h = 0; h < mapSize[index][0]; h++)
        {
            for (int w = 0; w < mapSize[index][1]; w++)
            {
                cls_index = 0;
                cls_vlaue = -100000;
                gridIndex += 2;

                for(int cl = 64; cl < 64+class_num; cl ++)
                {
                    cls_temp = output[cl * mapSize[index][0] * mapSize[index][1] + h * mapSize[index][1] + w];
        
                    if (cls_temp > cls_vlaue)
                    {
                        cls_vlaue = cls_temp;
                        cls_index = cl-64;
                    }
                }

                cls_vlaue = sigmoid(cls_vlaue);
                std::vector<float> regdfl;
                float regdeq[16] = {0};
                if (cls_vlaue > score_threshold_)
                {
                    regdfl.clear();
                    for (int lc = 0; lc < 4; lc++)
                    {
                        sfsum = 0;
                        locval = 0;
                        for (int df = 0; df < 16; df++)
                        {
                            locvaltemp = exp(output[((lc * 16) + df) * mapSize[index][0] * mapSize[index][1] + h * mapSize[index][1] + w]);
                            regdeq[df] = locvaltemp;
                            sfsum += locvaltemp;
                        }
                        for (int df = 0; df < 16; df++)
                        {
                            locvaltemp = regdeq[df] / sfsum;
                            locval += locvaltemp * df;
                        }
                        regdfl.push_back(locval);
                    }

                    BBox temp;
                    temp.xmin = (meshgrid[gridIndex + 0] - regdfl[0]) * strides[index];
                    temp.ymin = (meshgrid[gridIndex + 1] - regdfl[1]) * strides[index];
                    temp.xmax = (meshgrid[gridIndex + 0] + regdfl[2]) * strides[index];
                    temp.ymax = (meshgrid[gridIndex + 1] + regdfl[3]) * strides[index];
                    temp.label = cls_index;
                    temp.score = cls_vlaue;

                    detectRects.push_back(temp);
                }


            }
        }
    }

    return detectRects;
}

