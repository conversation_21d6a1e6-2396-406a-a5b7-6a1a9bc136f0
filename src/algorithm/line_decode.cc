#include "line_decode.h"
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <iomanip>
#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <unordered_map>
#include "utils.h"
#include "data_type.h"

std::vector<BBox> line_post_process(float **outputs, int class_num, int input_img_w,int input_img_h, float score_threshold_)
{
    int headNum = 1;
    int strides[1] = {8};
    int mapSize[1][2] = {{80, 80}};
    float cls_temp = 0;
    float cls_vlaue = 0;
    float w_offset = 0;
    float h_offset = 0;
    std::vector<BBox> detectPoints;


    float *output = outputs[0];

    for (int h = 0; h < mapSize[0][0]; h++)
    {
        for (int w = 0; w < mapSize[0][1]; w++)
        {
            cls_vlaue = -100000;
            cls_temp = output[2* mapSize[0][0] * mapSize[0][1] + h * mapSize[0][1] + w];
            cls_vlaue = sigmoid(cls_temp);
            if (cls_vlaue > score_threshold_)
            {
                w_offset = output[h * mapSize[0][1] + w];
                h_offset = output[ mapSize[0][0] * mapSize[0][1] + h * mapSize[0][1] + w];
                BBox temp;
                temp.xmin = (w+w_offset)*strides[0];
                temp.ymin = (h+h_offset)*strides[0];
                temp.xmax = (w+w_offset)*strides[0];
                temp.ymax = (h+h_offset)*strides[0];
                temp.label = 1;
                temp.score = cls_vlaue;
                detectPoints.push_back(temp);
                // printf("xmin = %f,ymin = %f, classId = %d, score = %f\n",temp.xmin,temp.ymin,temp.label,temp.score);
                }
            }
        }
    return detectPoints;

}
