#ifndef LINE_DET_H
#define LINE_DET_H
#include <string>
#include <npulib.h>
#include "base_model.h"
#include "npulib.h"
#include "line_decode.h"
#include "data_type.h"

#define ori_img_w 1280
#define ori_img_h 720
#define GROUND_REFINE_SCORE 1
#define LINE_TOP_K 20
#define FILTERBOX 1
#define SHOW_DETECTIONOUTPUT_LOG 0
// #define nms_threshold 0.45

#define ioa_threshold 0.5
#define threshold_float 0.3
#define rug_iou_thresh 0.1
#define rug_scales_iou_thresh 0.7

#define uchair_threshold 0.5


class shunzaoAiTask;

class LineDet: public NetworkBase
{

public:
    LineDet(const char* model_path);
    ~LineDet();
    // 初始化模型，传入模型路径
    bool setup(const std::string& model_path);
    // 加载输入数据并进行推理和后处理
    bool run_inference(cv::Mat& img, InputParam* inp);
    
    bool run_inference_with_time(cv::Mat& img, InputParam* inp);

    bool run_inference(cv::Mat& , InputParam* ,shunzaoAiTask* );

    // 获取输出并处理（此处示例返回第一个输出的最大值索引）
    std::vector<BBoxFlag> get_prediction(int* size);

    // char* getResults(int* size);
    int loadconfig(std::string config_string);
private:
    int postprocess(cv::Mat& img, float** output_tensor);

    // void init();
private:
    // NetworkItem Net;
    int eval = 0;
    int debug_show_result = 1;
    /* data */
    int input_h = 640;
    int input_w = 640;
    int input_c = 3;
    float score_threshold_ = 0.8;
    float nms_threshold_ =0.45;
    std::vector<float> mean_score_={
        0.67094476
    };
    int class_num = 1;
    std::vector<int> heads_list ={7,2,5,1};
    // float** gound_output_data = nullptr;

    int top_k_ = 30;
    int max_data_size_ = top_k_ * sizeof(BBoxFlag) + 12 + 4 + 1;
    std::vector<BBox> results_;
    char* res_addr_;

    std::vector<BBox> det_boxes_results;


};


#endif