#ifndef SLAM_DET_H
#define SLAM_DET_H
#include <string>
#include <npulib.h>
#include "base_model.h"
#include "npulib.h"
#include "yolov8.h"
#include "data_type.h"

#define SLAM_BOXES_FILTER_TH 0.3
#define SLAM_ori_img_w 640
#define SLAM_ori_img_h 640
#define SLAM_REFINE_SCORE 1
#define SLAM_top_k 20
#define SLAM_FILTERBOX 1
#define SLAM_SHOW_DETECTIONOUTPUT_LOG 0
#define SLAM_ioa_threshold 0.5
#define SLAM_threshold_float 0.3
#define SLAM_nms_threshold 0.45

// SLAM检测的6个类别索引定义
#define SLAM_CLASS_0_INDEX 0
#define SLAM_CLASS_1_INDEX 1
#define SLAM_CLASS_2_INDEX 2
#define SLAM_CLASS_3_INDEX 3
#define SLAM_CLASS_4_INDEX 4
#define SLAM_CLASS_5_INDEX 5

class shunzaoAiTask;

class SlamDet: public NetworkBase
{

public:
    SlamDet(const char* model_path);
    ~SlamDet();
    // 初始化模型，传入模型路径
    bool setup(const std::string& model_path);
    // 加载输入数据并进行推理和后处理
    bool run_inference(cv::Mat& img, InputParam* inp);
    
    bool run_inference_with_time(cv::Mat& img, InputParam* inp);

    //加载输入数据并进行推理和后处理，并且计时
    bool run_inference(cv::Mat& , InputParam* ,shunzaoAiTask* );
        // 获取输出并处理（此处示例返回第一个输出的最大值索引）
    std::vector<BBoxFlag> get_prediction(int* size);

    // char* getResults(int* size);
    int loadconfig(std::string config_string);
private:
    int postprocess(cv::Mat& img, float** output_tensor);

    // void init();
private:
    // NetworkItem Net;
    int eval = 0;
    int debug_show_result = 1;
    /* data */
    int input_h = 640;
    int input_w = 640;
    int input_c = 3;
    float score_threshold_ = 0.2;
    float nms_threshold_ = 0.7;
    
    // SLAM检测6个类别的平均分数
    std::vector<float> mean_score_={
        0.67094476,
        0.79869564,
        0.70399142,
        0.61118492,
        0.66352386,
        0.49859135,
        0.49170399,
        0.60432732,
        0.714039658,
        0.786953375,
        0.60063568,
    };
    
    // SLAM检测有6个类别
    int class_num = 6;

    // 单个检测头，三个尺度，检测6个类别
    std::vector<int> heads_list = {6}; // 一个头检测6个类别
    
    int top_k_ = 30;
    int max_data_size_ = top_k_ * sizeof(BBoxFlag) + 12 + 4 + 1;
    std::vector<BBox> results_;
    char* res_addr_;

    std::vector<BBox> det_boxes_results;
    std::vector<BBoxFlag> det_boxes_flag_results;

};

#endif
