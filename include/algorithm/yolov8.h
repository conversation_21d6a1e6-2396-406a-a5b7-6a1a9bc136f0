#ifndef YOLOV8_H
#define YOLOV8_H
#include <opencv2/core/core.hpp>
#include "data_type.h"
 

// std::vector<float> meshgrid;
// int class_num = 80;
// int headNum = 3;

// int strides[3] = {8, 16, 32};
// int mapSize[3][2] = {{80, 80}, {40, 40}, {20, 20}};
// std::vector<float> regdfl;
// float regdeq[16] = {0};

std::vector<BBox> yolov8_post_process(float **output, int class_num, int input_img_w,int input_img_h, float score_threshold_);
#endif