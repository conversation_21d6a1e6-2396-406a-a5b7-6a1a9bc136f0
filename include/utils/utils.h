#pragma once 
#include <fstream>
#include <iostream>
#include <iterator>
#include <string.h>
#include <vector>
#include <cmath>
#include <sys/time.h>
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc.hpp"
#include <unordered_map>

// #include "bpu_predict_extension.h"
// #include "openssl/aes.h"
// #include "basic_data.h"
#include "data_type.h"

#define MSG_PRINTF(flag, fmt,...)\
if(flag){\
printf(fmt, ## __VA_ARGS__);\
}

#define PI       3.14159265358979323846

const int color_list[80][3] =
{
    //{255 ,255 ,255}, //bg
    {216 , 82 , 24},
    {236 ,176 , 31},
    {125 , 46 ,141},
    {118 ,171 , 47},
    { 76 ,189 ,237},
    {238 , 19 , 46},
    { 76 , 76 , 76},
    {153 ,153 ,153},
    {255 ,  0 ,  0},
    {255 ,127 ,  0},
    {190 ,190 ,  0},
    {  0 ,255 ,  0},
    {  0 ,  0 ,255},
    {170 ,  0 ,255},
    { 84 , 84 ,  0},
    { 84 ,170 ,  0},
    { 84 ,255 ,  0},
    {170 , 84 ,  0},
    {170 ,170 ,  0},
    {170 ,255 ,  0},
    {255 , 84 ,  0},
    {255 ,170 ,  0},
    {255 ,255 ,  0},
    {  0 , 84 ,127},
    {  0 ,170 ,127},
    {  0 ,255 ,127},
    { 84 ,  0 ,127},
    { 84 , 84 ,127},
    { 84 ,170 ,127},
    { 84 ,255 ,127},
    {170 ,  0 ,127},
    {170 , 84 ,127},
    {170 ,170 ,127},
    {170 ,255 ,127},
    {255 ,  0 ,127},
    {255 , 84 ,127},
    {255 ,170 ,127},
    {255 ,255 ,127},
    {  0 , 84 ,255},
    {  0 ,170 ,255},
    {  0 ,255 ,255},
    { 84 ,  0 ,255},
    { 84 , 84 ,255},
    { 84 ,170 ,255},
    { 84 ,255 ,255},
    {170 ,  0 ,255},
    {170 , 84 ,255},
    {170 ,170 ,255},
    {170 ,255 ,255},
    {255 ,  0 ,255},
    {255 , 84 ,255},
    {255 ,170 ,255},
    { 42 ,  0 ,  0},
    { 84 ,  0 ,  0},
    {127 ,  0 ,  0},
    {170 ,  0 ,  0},
    {212 ,  0 ,  0},
    {255 ,  0 ,  0},
    {  0 , 42 ,  0},
    {  0 , 84 ,  0},
    {  0 ,127 ,  0},
    {  0 ,170 ,  0},
    {  0 ,212 ,  0},
    {  0 ,255 ,  0},
    {  0 ,  0 , 42},
    {  0 ,  0 , 84},
    {  0 ,  0 ,127},
    {  0 ,  0 ,170},
    {  0 ,  0 ,212},
    {  0 ,  0 ,255},
    {  0 ,  0 ,  0},
    { 36 , 36 , 36},
    { 72 , 72 , 72},
    {109 ,109 ,109},
    {145 ,145 ,145},
    {182 ,182 ,182},
    {218 ,218 ,218},
    {  0 ,113 ,188},
    { 80 ,182 ,188},
    {127 ,127 ,  0},
};

typedef struct RGBArg
{
    float R;
    float G;
    float B;
} RGBArg;

typedef struct CenterScale
{
    cv::Point2f center;
    cv::Point2f scale;
} CenterScale;

typedef struct CropImage
{
    cv::Mat img;
    CenterScale cs;
} CropImage;

typedef struct Size
{
    int w;
    int h;
} Size;

typedef struct BBoxXYWH
{
    float xmin;
    float ymin;
    float w;
    float h;
    float catid;
    float score;
} BBoxXYWH;

typedef struct BodyBox{
    float xmin;
    float ymin;
    float xmax;
    float ymax;
    float label;
    float score;
    float btag;
    float undistored; //是否去畸变
}BodyBox;

// typedef struct ai_msg
// {
//     uint32_t data_size;
//     uint32_t model_id;
//     // uint32_t core_id;
//     uint64_t frameid;
//     uint64_t data;
//     // uint64_t msg_num;
//     /* data */
// }ai_msg_t;

typedef struct MovingRobot
{
    float moving_distance;
    float movding_dirction;
}MovingRobot;


BBox box_transform(BBox ori, int nn_h, int nn_w, int ori_h, int ori_w);
typedef struct std::vector<float> tags;
BBoxXYWH xyxy2xywh(BBox& bbox);
std::vector<BBoxXYWH> xyxy2xywh(std::vector<BBox>& bboxes);
std::vector<CropImage> crop_image_by_bbox(cv::Mat image, std::vector<BBoxXYWH>& bboxes, Size input_size, float scale_rate);
cv::Point2f transform_preds(cv::Point2f coords, CenterScale cs, Size output_size);

// HKP trans_kps(HKP kps, cv::Size input_shape);
// HKP pixcel_camera_transfer(HKP sk, std::array<float, 2> focal_cam1, std::array<float, 2> princpt_cam1,
//     std::array<float, 2> focal_cam2, std::array<float, 2> princpt_cam2);
// void pixel2cam(HKP3D& pixel_coord, std::array<float, 2> f, std::array<float, 2> c);
void draw_box(cv::Mat image, BBox bbox, cv::Scalar color);


CenterScale get_box_cs(BBoxXYWH box, Size input_size, float scale_rate);
float get_iou(BBox b1, BBox b2);

float CalculateIOU(const BBox& a, const BBox& b);
int NMS(std::vector<BBox> &detectRects, float nms_thresh);
float sigmoid(float x);
int GenerateMeshgrid(int headNum, int mapSize[1][2], std::vector<float> &meshgrid);
void draw_objects(cv::Mat& image_data, const std::vector<BBoxFlag>& objects, std::string image_path);
void draw_line(cv::Mat& image_data, const std::vector<BBoxFlag>& objects, std::string image_path);
void save_result_as_txt(cv::Mat& image_data, const std::vector<BBoxFlag>& objects, std::string image_path);

void draw_slam_objects(cv::Mat& image_data, const std::vector<BBoxFlag>& objects, std::string image_path);
void save_slam_result_as_txt(const std::vector<BBoxFlag>& objects, std::string image_path);

// SLAM分割相关函数
void draw_slam_seg(cv::Mat& image_data, const std::vector<BBoxFlag>& objects, std::string image_path);
void draw_slam_seg_mask(cv::Mat& image_data, const std::vector<SegMaskFlag>& seg_results, std::string image_path);
void save_slam_seg_result_as_txt(const std::vector<SegMaskFlag>& seg_results, std::string image_path);


// 自定义 clamp 函数（兼容 C++11）
template<typename T>
T clamp(T value, T min_val, T max_val) {
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return value;
}

class shunzaoTimer{
    public:
        shunzaoTimer(){};
        void start(){
            gettimeofday(&run_start,nullptr);
        };
        void printTime(const char* timename, int flag = 0){
            gettimeofday(&run_end,nullptr);
            auto alloc_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                              (run_end.tv_usec - run_start.tv_usec);
            if(flag){
                printf("%s time: %.3f ms\n", timename, static_cast<double>(alloc_time/1000));
            }
            
    
        }
    // private:
    
        struct  timeval run_start{},run_end{};
        
    
    };