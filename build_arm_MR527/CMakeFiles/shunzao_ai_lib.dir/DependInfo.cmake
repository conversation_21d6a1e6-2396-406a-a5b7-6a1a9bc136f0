
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp" "CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp" "CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/line_decode.cc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov5.cc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov5s_pre.cpp" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov8.cc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/basic_model/base_model.cc" "CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/shunzao_ai_task.cc" "CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/ground_det.cc" "CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/line_det.cc" "CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_det.cc" "CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_seg.cc" "CMakeFiles/shunzao_ai_lib.dir/src/task/slam_seg.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/task/slam_seg.cc.o.d"
  "/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/utils/utils.cc" "CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o" "gcc" "CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
