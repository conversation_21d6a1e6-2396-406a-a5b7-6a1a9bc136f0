# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527

# Include any dependencies generated for this target.
include CMakeFiles/shunzao_ai_demo.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/shunzao_ai_demo.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/shunzao_ai_demo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/shunzao_ai_demo.dir/flags.make

CMakeFiles/shunzao_ai_demo.dir/main.cc.o: CMakeFiles/shunzao_ai_demo.dir/flags.make
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: ../main.cc
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: CMakeFiles/shunzao_ai_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/shunzao_ai_demo.dir/main.cc.o"
	/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/shunzao_ai_demo.dir/main.cc.o -MF CMakeFiles/shunzao_ai_demo.dir/main.cc.o.d -o CMakeFiles/shunzao_ai_demo.dir/main.cc.o -c /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/main.cc

CMakeFiles/shunzao_ai_demo.dir/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_demo.dir/main.cc.i"
	/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/main.cc > CMakeFiles/shunzao_ai_demo.dir/main.cc.i

CMakeFiles/shunzao_ai_demo.dir/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_demo.dir/main.cc.s"
	/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/main.cc -o CMakeFiles/shunzao_ai_demo.dir/main.cc.s

# Object files for target shunzao_ai_demo
shunzao_ai_demo_OBJECTS = \
"CMakeFiles/shunzao_ai_demo.dir/main.cc.o"

# External object files for target shunzao_ai_demo
shunzao_ai_demo_EXTERNAL_OBJECTS =

shunzao_ai_demo: CMakeFiles/shunzao_ai_demo.dir/main.cc.o
shunzao_ai_demo: CMakeFiles/shunzao_ai_demo.dir/build.make
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_core.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgproc.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgcodecs.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_highgui.a
shunzao_ai_demo: libshunzao_ai_lib.so
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_highgui.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_videoio.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgcodecs.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgproc.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_core.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libittnotify.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibjpeg-turbo.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibwebp.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibpng.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibtiff.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libzlib.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibopenjp2.a
shunzao_ai_demo: /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libtegra_hal.a
shunzao_ai_demo: ../../common/lib_linux_aarch64/MR527/libVIPlite.so
shunzao_ai_demo: ../../common/lib_linux_aarch64/MR527/libVIPuser.so
shunzao_ai_demo: CMakeFiles/shunzao_ai_demo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable shunzao_ai_demo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/shunzao_ai_demo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/shunzao_ai_demo.dir/build: shunzao_ai_demo
.PHONY : CMakeFiles/shunzao_ai_demo.dir/build

CMakeFiles/shunzao_ai_demo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/shunzao_ai_demo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/shunzao_ai_demo.dir/clean

CMakeFiles/shunzao_ai_demo.dir/depend:
	cd /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527 && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527 /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527 /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527/CMakeFiles/shunzao_ai_demo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/shunzao_ai_demo.dir/depend

