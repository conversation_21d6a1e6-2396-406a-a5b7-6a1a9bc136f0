# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++
CXX_DEFINES = -D_GLIBCXX_USE_CXX11_ABI=1

CXX_INCLUDES = -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../common/include -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/../common -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/include -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/include/algorithm -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/include/utils -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/include/basic_model -I/home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/include/task -isystem /home/<USER>/panpan/code/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4

CXX_FLAGS =  -Wall -Wextra -O2 -fopenmp -fdiagnostics-color=always -D_GLIBCXX_USE_CXX11_ABI=0 -std=gnu++11

