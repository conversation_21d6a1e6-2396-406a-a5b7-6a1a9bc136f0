#!/bin/bash
set -e  # 任何命令失败就退出

# 检查参数是否提供
if [ $# -ne 1 ]; then
    echo "Usage: $0 [MR527|MR536]"
    exit 1
fi

# 检查参数合法性
if [ "$1" != "MR527" ] && [ "$1" != "MR536" ]; then
    echo "Invalid argument: $1"
    echo "Usage: $0 [MR527|MR536]"
    exit 1
fi

# 设置变量
TARGET_BOARD=$1
BUILD_DIR=build_arm_${TARGET_BOARD}

# 清理旧目录
rm -rf ${BUILD_DIR}

# 创建并进入构建目录
mkdir -p ${BUILD_DIR}
cd ${BUILD_DIR}

# 配置 CMake 构建系统，并将安装路径设为当前构建目录
cmake .. -DCMAKE_INSTALL_PREFIX=$(pwd) -DTARGET_BOARD=${TARGET_BOARD}

# 编译项目
make -j$(nproc)

# 安装到 build_arm/bin
make install

# 检查 bin 下是否存在可执行文件（比如 shunzao_ai_demo）
if [ -x bin/shunzao_ai_demo ]; then
    echo "✅ 安装成功：shunzao_ai_demo 已安装到 build_arm/bin/"
else
    echo "❌ 安装失败：shunzao_ai_demo 不在 build_arm/bin/ 中或不可执行"
    exit 1
fi

